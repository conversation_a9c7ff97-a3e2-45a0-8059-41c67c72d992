"use client"

import { But<PERSON>, useToast } from "@apollo/ui"

import { ComponentGroup } from "./common"

export function Toasts() {
  const { add } = useToast()
  return (
    <ComponentGroup>
      <Button
        onClick={() =>
          add({
            title: "Hello World",
            description: "This is a toast",
            type: "success",
          })
        }
      >
        Click Me
      </Button>
      <Button
        onClick={() =>
          add({
            title: "Hello World",
            description: "This is a toast",
            position: ["left", "up"],
          })
        }
      >
        Top Left
      </Button>
    </ComponentGroup>
  )
}
