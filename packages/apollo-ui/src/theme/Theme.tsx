import {
  Children,
  cloneElement,
  isValidElement,
  ReactElement,
  ReactNode,
  useId,
  useLayoutEffect,
  useMemo,
  useState,
} from "react"
import { ApolloDefineToken, ApolloToken } from "@apollo/token"

import { reset } from "./override/reset"
import { animationStyles } from "./override/animation"
import { baseline } from "./override/baseline"
import { reactDatePickerStyleOverride } from "./override/react-datepicker"
import { zIndexStyles } from "./override/zIndex"
import type {
  DeepPartial,
  ParentPropsTheme,
  ThemeConfig,
  ThemeProps,
} from "./types"
import { overrideExistedKey, parseTokenV2, resolveTheme } from "./utils"
import { useInjectStyles } from "./hooks"

const baseTheme: ThemeConfig = {
  tokens: ApolloToken,
  inherit: true,
}

const overrideCSS = [
    reactDatePickerStyleOverride,
    animationStyles,
    zIndexStyles,
    baseline,
  ].join("\n")
const rootTokenCSS = parseTokenV2(baseTheme.tokens as ApolloDefineToken)
const rootThemeCSS = rootTokenCSS ? `@layer base {:root {${rootTokenCSS}}}` : ""


export function Theme<WrapperComponentType>({
  children,
  theme: propsTheme,
  mode,
  parentProps,
}: ThemeProps<WrapperComponentType>) {
  useInjectStyles("css-overrides", overrideCSS)
  useInjectStyles("css-global", reset)
  useInjectStyles("css-tokens", rootThemeCSS)

  const [isClient, setIsClient] = useState(false)
  useLayoutEffect(() => {
    setIsClient(true)
  }, [])

  const uniqueId = useId()
  const scopeId = `css-${uniqueId.toLocaleLowerCase().replace(/:/g, "")}`

  // Create theme context value - memoized to prevent unnecessary re-renders
  const resolvedTheme = useMemo(() => {
    return resolveTheme({
      scopeId,
      localTheme: propsTheme ?? {},
      parentTheme: parentProps?.theme,
      mode: mode,
      parentMode: parentProps?.mode,
    })
  }, [scopeId, propsTheme, mode, parentProps])

  useInjectStyles(scopeId, resolvedTheme.css as string)

  const processedChildren = useMemo(() => {
    const nextParentProps: ParentPropsTheme = {
      theme: resolvedTheme.theme as ThemeConfig,
      mode: resolvedTheme.mode as "light" | "dark",
    }

    const processNode = (node: ReactNode): ReactNode => {
      return Children.map(node, (child) => {
        const element = child as ReactElement<{ children: ReactNode }>
        if (!isValidElement(child)) return child

        if (element.type === Theme) {
          return cloneElement(element, {
            key: `${element.key || ""}-${nextParentProps.mode}`,
            parentProps: nextParentProps,
          } as { key: string; parentProps: ParentPropsTheme })
        }

        // If the child has its own children, recurse into them.
        if (element.props.children) {
          return cloneElement(element, {
            ...element.props,
            children: processNode(element.props.children),
          })
        }

        return element
      })
    }
    return processNode(children)
  }, [children, resolvedTheme.theme, resolvedTheme.mode])

  return (
    isClient && (
        <div className={scopeId} data-apl-theme={scopeId} style={{ display: "contents" }}>
          {processedChildren}
        </div>
    )
  )
}

// Utility function to create a theme configuration
export function createTheme(
  themeConfig?: DeepPartial<ThemeConfig>
): ThemeConfig {
  return themeConfig ? overrideExistedKey(themeConfig, baseTheme) : {}
}
