import { ReactNode } from "react";
import { Theme } from "../../theme";
import { ToastProvider } from "../toast";
import { ApolloProviderConfig, ApolloProviderType } from "./ApolloProviderType";


// This component acts as a base case, just rendering its children.
// const InitialProvider: ApolloProviderType = ({ children }) => <>{children}</>;

/**
 * Chains multiple providers into a single nested component.
 * Safely handles an empty providers array.
 */
export const composeProviders = (providersConfig: ApolloProviderConfig[]): ApolloProviderType => {
  // Return a new component that will receive all the props.
  return ({ children, ...rest }) => {
    // Use reduceRight to nest providers in the correct order (first in array is outermost).
    // The initial value of the accumulator is the children.
    return providersConfig.reduceRight(
      (acc: ReactNode, { component: Provider, propName }: ApolloProviderConfig) => {
        const props = (rest as Record<string, unknown>)[propName] || {};
        // Wrap the accumulated components (`acc`) with the current provider.
        return <Provider {...props}>{acc}</Provider>;
      },
      children
    );
  };
};


const providersConfig = [
  { component: Theme, propName: 'themeProps' },
  { component: ToastProvider, propName: 'toastProps' }
];
export const ApolloProvider = composeProviders(providersConfig);
