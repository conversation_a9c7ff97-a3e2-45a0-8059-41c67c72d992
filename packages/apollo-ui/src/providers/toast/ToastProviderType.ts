import { PropsWithChildren } from "react"
import { Toast as BaseToast } from "@base-ui-components/react/toast"
import { ToastObjectProps } from "../../components"

export type ToastProviderProps = BaseToast.Provider.Props
export type ToastProviderType = PropsWithChildren<ToastProviderProps>

export type ToastContextType = Omit<BaseToast.useToastManager.ReturnValue, "toasts" | "add" | "update"> & {
  toasts: ToastObjectProps[]
  add: (options: Omit<ToastObjectProps, 'id' | 'open' | 'onOpenChange'>) => void
  update: (toastId: string, options: BaseToast.useToastManager.UpdateOptions<ToastObjectProps>) => void
}
