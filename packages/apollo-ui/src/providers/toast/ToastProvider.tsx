import { Toast as BaseToast } from "@base-ui-components/react/toast"

import { Portal, } from "../../components"
import { ToastContainer } from "../../components/toast/ToastContainer"
import {  ToastProviderType } from "./ToastProviderType"

export const ToastProvider = ({ children, ...props }: ToastProviderType) => {
  return (
    <BaseToast.Provider {...props}>
      <Portal baseComponent={<BaseToast.Portal />}>
        <ToastContainer />
        {children}
      </Portal>
    </BaseToast.Provider>
  )
}
