import { Toast as BaseToast } from "@base-ui-components/react"

import { ToastObjectProps } from "../components"

export const useToast = () => {
  const toastManager = BaseToast.useToastManager()

  const toasts = toastManager.toasts as ToastObjectProps[]
  const add = (
    options: Omit<ToastObjectProps, 'id' | 'open' | 'onOpenChange'> & {
      id?:string
    }
  ) => {
    toastManager.add(options as BaseToast.useToastManager.AddOptions<ToastObjectProps>)
  }

  const update = (
    toastId: string,
    options: BaseToast.useToastManager.UpdateOptions<ToastObjectProps>
  ) => {
    toastManager.update(toastId, options)
  }

  return {
    ...toastManager,
    toasts,
    add,
    update,
  }
}
