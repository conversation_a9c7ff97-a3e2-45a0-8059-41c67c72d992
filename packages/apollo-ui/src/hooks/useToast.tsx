import { Toast as BaseToast } from "@base-ui-components/react"

import { ToastObjectProps } from "../components"

export const useToast = () => {
  const toastManager = BaseToast.useToastManager()

  const toasts = toastManager.toasts as ToastObjectProps[]
  const add = (
    options: BaseToast.useToastManager.AddOptions<ToastObjectProps>
  ) => {
    // options.data.po
    toastManager.add(options)
  }

  const update = (
    toastId: string,
    options: BaseToast.useToastManager.UpdateOptions<ToastObjectProps>
  ) => {
    toastManager.update(toastId, options)
  }

  return {
    ...toastManager,
    toasts,
    add,
    update,
  }
}
