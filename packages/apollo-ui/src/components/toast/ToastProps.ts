import { PropsWithChildren } from "react"
import { Toast } from "@base-ui-components/react"

import { AlertProps } from "../alert"

export type TypeProp = "success" | "info" | "warning" | "error"

export type SwipeDirectionProp =
  | "left"
  | "right"
  | "up"
  | "down"
  | ("left" | "right" | "up" | "down")[]

export type PositionProp =
  | "top-center"
  | "top-right"
  | "top-left"
  | "bottom-center"
  | "bottom-right"
  | "bottom-left"

export interface ToastRef extends HTMLDivElement {
  getPosition: () => PositionProp
}

export type ToastObjectProps = Omit<
  Toast.Root.ToastObject,
  "type" | "actionProps"
> & {
  swipeDirection?: SwipeDirectionProp
  position?: PositionProp
  type?: TypeProp
  className?: string
} & Pick<AlertProps, "startDecorator" | "endDecorator" | "onClose" | "action">

export type ToastProps = Omit<Toast.Root.Props, "toast" | "swipeDirection"> & {
  toast: ToastObjectProps
}

export type ToastContainerProps = PropsWithChildren<{
  toasts: ToastObjectProps[]
}>
