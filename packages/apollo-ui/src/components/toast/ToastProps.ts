import { PropsWithChildren, ReactNode } from "react"
import { Toast } from "@base-ui-components/react"

export type TypeProp = "success" | "info" | "warning" | "error"

export type DirectionProp =
  | "left"
  | "right"
  | "up"
  | "down"
  | ("left" | "right" | "up" | "down")[]

export type ToastData = {
  startDecorator?: ReactNode
  endDecorator?: ReactNode
  onClose?: () => void
  action?: ReactNode
}

export type ToastObjectProps = Omit<
  Toast.Root.ToastObject,
  "data" | "type"
> & {
  position?: DirectionProp
  type?: TypeProp
  data?: ToastData
  className?: string
}

export type ToastProps = Omit<Toast.Root.Props, "toast" | "swipeDirection"> & {
  toast: ToastObjectProps
}

export type ToastContainerProps = PropsWithChildren<{
  toasts: ToastObjectProps[]
}>
