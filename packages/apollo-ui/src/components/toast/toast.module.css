@keyframes slideInRight {
  from {
    transform: translateX(120%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(120%);
    opacity: 0;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(120%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(120%);
    opacity: 0;
  }
}


@keyframes slideInTop {
  from {
    transform: translateY(-120%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutTop {
  from {
    transform: translateY(0);
    opacity: 1;
  }

  to {
    transform: translateY(-120%);
    opacity: 0;
  }
}

@keyframes slideInBottom {
  from {
    transform: translateY(-120%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutBottom {
  from {
    transform: translateY(0);
    opacity: 1;
  }

  to {
    transform: translateY(-120%);
    opacity: 0;
  }
}

.toastContainer {
  position: absolute;
  top: 2%;
  right: 2%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 9999;
}

.toastTopLeft {
  top: 2%;
  left: 2%;
}

.toastTopCenter {
  top: 2%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.toastTopRight {
  top: 2%;
  right: 2%;
}

.toastBottomLeft {
  bottom: 2%;
  left: 2%;
}

.toastBottomCenter {
  bottom: 2%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.toastBottomRight {
  bottom: 2%;
  right: 2%;
}

/* .toastRoot {
  width: 328px;
  position: relative;
  animation: slideInLeft 0.3s forwards;
} */

.toastRootClosing {
  animation: slideOutLeft 0.3s forwards;
}

.toastSlideInLeft {
  animation: slideInLeft 0.3s forwards;
}

.toastSlideOutLeft {
  animation: slideOutLeft 0.3s forwards;
}

.toastSlideInRight {
  animation: slideInRight 0.3s forwards;
}

.toastSlideOutRight {
  animation: slideOutRight 0.3s forwards;
}

.toastSlideInTop {
  animation: slideInTop 0.3s forwards;
}

.toastSlideOutTop {
  animation: slideOutTop 0.3s forwards;
}

.toastSlideInBottom {
  animation: slideInBottom 0.3s forwards;
}

.toastSlideOutBottom {
  animation: slideOutBottom 0.3s forwards;
}

.toastViewport {
  position: fixed;
  z-index: 1;
  width: 100%;
  margin: 0 auto;
  /* bottom: 1rem;
  right: auto;
  left: auto;
  top: auto; */
   top: 1rem;
  right: 0;
  left: 0;
  bottom: auto;
max-width: 300px;
}

/* .toastViewportTop {
  margin: 0 auto;
  bottom: 1rem;
  right: 1rem;
  left: auto;
  top: auto;

  top: 1rem;
  right: 0;
  left: 0;
  bottom: auto;
} */

.toastRoot {
  --gap: 0.75rem;
  /* --offset-y: calc(var(--toast-offset-y) + (var(--toast-index) * var(--gap)) + var(--toast-swipe-movement-y)); */
  --offset-y: calc(var(--toast-offset-y) * -1 + (var(--toast-index) * var(--gap) * -1) + var(--toast-swipe-movement-y));
  position: absolute;
  left: 0;
  /* right: 0; */
  min-width: 328px;
  background-clip: padding-box;
  /* top: 0; */
  /* left: 0;
  right: 0; */
  bottom: 0;
  right: auto;
  margin: 0 auto;

  margin-right: auto;
  margin-left: auto;
  -webkit-user-select: none;
  user-select: none;
  transition:
    transform 0.5s cubic-bezier(0.22, 1, 0.36, 1),
    opacity 0.5s;
  cursor: default;
  z-index: calc(1000 - var(--toast-index));
  transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--toast-swipe-movement-y) + (min(var(--toast-index), 10) * -20%))) scale(calc(max(0, 1 - (var(--toast-index) * 0.1))));
  /* transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--toast-swipe-movement-y) + (min(var(--toast-index), 10) * 20%))) scale(calc(max(0, 1 - (var(--toast-index) * 0.1)))); */

  &::after {
    bottom: 100%;
  }

  &[data-expanded] {
    transform: translateX(var(--toast-swipe-movement-x)) translateY(calc(var(--offset-y)));
  }

  &[data-starting-style],
  &[data-ending-style] {
    transform: translateY(-150%);
  }

  &[data-limited] {
    opacity: 0;
  }

  &[data-ending-style] {
    opacity: 0;

    &[data-swipe-direction='up'] {
      transform: translateY(calc(var(--toast-swipe-movement-y) - 150%));
    }

    &[data-swipe-direction='left'] {
      transform: translateX(calc(var(--toast-swipe-movement-x) - 150%)) translateY(var(--offset-y));
    }

    &[data-swipe-direction='right'] {
      transform: translateX(calc(var(--toast-swipe-movement-x) + 150%)) translateY(var(--offset-y));
    }

    &[data-swipe-direction='down'] {
      transform: translateY(calc(var(--toast-swipe-movement-y) + 150%));
    }
  }

  &::after {
    content: '';
    position: absolute;
    width: 100%;
    left: 0;
    height: calc(var(--gap) + 1px);
  }
}
