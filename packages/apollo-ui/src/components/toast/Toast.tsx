import { Toast as BaseToast } from "@base-ui-components/react"
import classNames from "classnames"

import { Alert } from "../alert"
import styles from "./toast.module.css"
import { ToastProps } from "./ToastProps"

export function Toast({ toast, ...toastRootProps }: ToastProps) {
  const swipeDirection = toast.position
  return (
    <BaseToast.Root
      {...toastRootProps}
      toast={toast}
      swipeDirection={swipeDirection}
      className={classNames("ApolloToast-root", styles.toastRoot)}
    >
      <Alert
        color={toast.type}
        title={toast.title}
        description={toast.description}
        startDecorator={toast.data?.startDecorator}
        endDecorator={toast.data?.endDecorator}
        action={toast.data?.action}
        onClose={toast.onClose}
        fullWidth
        className={classNames("ApolloToast-alert", toast.className)}
      />
    </BaseToast.Root>
  )
}
