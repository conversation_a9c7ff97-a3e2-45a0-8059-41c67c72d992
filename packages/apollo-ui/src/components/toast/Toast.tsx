import { Toast as BaseToast } from "@base-ui-components/react"
import classNames from "classnames"

import { Alert } from "../alert"
import styles from "./toast.module.css"
import { ToastProps, ToastRef } from "./ToastProps"
import { forwardRef, Ref, useImperativeHandle, useMemo } from "react"
import { cva } from "class-variance-authority"

const toastRootVariants = cva(styles.iconButtonRoot, {
  variants: {
    size: {
      large: styles.iconButtonLarge,
      medium: styles.iconButtonMedium,
      small: styles.iconButtonSmall,
    },
  },
  defaultVariants: {
    size: "large",
  },
})


export const Toast = forwardRef<
  ToastRef,
  ToastProps
>(function Toast({ toast, ...toastRootProps }, ref) {
  const position = useMemo(() => {
    return toast.position ?? "top-right"
  }, [toast.position])

   useImperativeHandle(ref, () => ({ getPosition: () => position }), [position]);

  return (
    <BaseToast.Root
      {...toastRootProps}
      toast={toast}
      swipeDirection={toast.swipeDirection}
      className={classNames("ApolloToast-root", styles.toastRoot)}
      ref={ref}
    >
      <Alert
        color={toast.type}
        title={toast.title}
        description={toast.description}
        startDecorator={toast.startDecorator}
        endDecorator={toast.endDecorator}
        action={toast.action}
        onClose={toast.onClose}
        fullWidth
        className={classNames("ApolloToast-alert", toast.className)}
        ref={toast.ref as Ref<HTMLDivElement>}
      />
    </BaseToast.Root>
  )
})
