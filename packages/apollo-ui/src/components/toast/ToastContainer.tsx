import { Toast as BaseToast } from "@base-ui-components/react/toast"

import { Toast } from "./Toast"
import styles from "./toast.module.css"
import { useToast } from "../../hooks"

// const toastViewportVariants = cva(styles.iconButtonRoot, {
//   variants: {
//     size: {
//       large: styles.iconButtonLarge,
//       medium: styles.iconButtonMedium,
//       small: styles.iconButtonSmall,
//     },
//   },
//   defaultVariants: {
//     size: "large",
//   },
// })

export function ToastContainer() {
  const { toasts } = useToast()
  return (
    <BaseToast.Viewport className={styles.toastViewport}>
      {toasts.map((toast) => (
        <Toast key={toast.id} toast={toast} />
      ))}
    </BaseToast.Viewport>
  )
}
