import { Toast as BaseToast } from "@base-ui-components/react/toast"

import { Toast } from "./Toast"
import styles from "./toast.module.css"
import { useToast } from "../../hooks"


export function ToastContainer() {
  const { toasts } = useToast()
  return (
    <BaseToast.Viewport className={styles.toastViewport}>
      {toasts.map((toast) => (
        <Toast key={toast.id} toast={toast} />
      ))}
    </BaseToast.Viewport>
  )
}
